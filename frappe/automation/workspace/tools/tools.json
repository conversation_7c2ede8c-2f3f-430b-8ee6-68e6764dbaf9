{"charts": [], "content": "[{\"id\":\"sR-UFcO7II\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Import Data\",\"col\":3}},{\"id\":\"IkcVmgWb3z\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"ToDo\",\"col\":3}},{\"id\":\"6wir-jZFRE\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"File\",\"col\":3}},{\"id\":\"45a1jzQkTm\",\"type\":\"shortcut\",\"data\":{\"shortcut_name\":\"Assignment Rule\",\"col\":3}},{\"id\":\"EgtURZsoiF\",\"type\":\"spacer\",\"data\":{\"col\":12}},{\"id\":\"0yceBIfhHM\",\"type\":\"card\",\"data\":{\"card_name\":\"Data\",\"col\":4}},{\"id\":\"42WbBA9rpj\",\"type\":\"card\",\"data\":{\"card_name\":\"Tools\",\"col\":4}},{\"id\":\"wE9n7TIrAc\",\"type\":\"card\",\"data\":{\"card_name\":\"Alerts and Notifications\",\"col\":4}},{\"id\":\"7_U7_xCOos\",\"type\":\"card\",\"data\":{\"card_name\":\"Email\",\"col\":4}},{\"id\":\"3imoh2oqsJ\",\"type\":\"card\",\"data\":{\"card_name\":\"Printing\",\"col\":4}},{\"id\":\"SlYKJZj5r3\",\"type\":\"card\",\"data\":{\"card_name\":\"Automation\",\"col\":4}}]", "creation": "2020-03-02 14:53:24.980279", "custom_blocks": [], "docstatus": 0, "doctype": "Workspace", "for_user": "", "hide_custom": 0, "icon": "tool", "idx": 0, "is_hidden": 0, "label": "Tools", "links": [{"hidden": 0, "is_query_report": 0, "label": "Automation", "link_count": 3, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Assignment Rule", "link_count": 0, "link_to": "Assignment Rule", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Milestone", "link_count": 0, "link_to": "Milestone", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Auto Repeat", "link_count": 0, "link_to": "Auto Repeat", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Tools", "link_count": 4, "onboard": 0, "type": "Card Break"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "To Do", "link_count": 0, "link_to": "ToDo", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Calendar", "link_count": 0, "link_to": "Event", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Note", "link_count": 0, "link_to": "Note", "link_type": "DocType", "onboard": 1, "type": "Link"}, {"dependencies": "", "hidden": 0, "is_query_report": 0, "label": "Files", "link_count": 0, "link_to": "File", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Printing", "link_count": 4, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Print Format Builder", "link_count": 0, "link_to": "print-format-builder", "link_type": "Page", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Print Format Builder (New)", "link_count": 0, "link_to": "print-format-builder-beta", "link_type": "Page", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Print Settings", "link_count": 0, "link_to": "Print Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Print Heading", "link_count": 0, "link_to": "Print Heading", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Alerts and Notifications", "link_count": 3, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Notification", "link_count": 0, "link_to": "Notification", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Auto Email Report", "link_count": 0, "link_to": "Auto Email Report", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Notification Settings", "link_count": 0, "link_to": "Notification Settings", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"description": "Manage your data", "hidden": 0, "is_query_report": 0, "label": "Data", "link_count": 5, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "Import Data", "link_count": 0, "link_to": "Data Import", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Export Data", "link_count": 0, "link_to": "Data Export", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Bulk Update", "link_count": 0, "link_to": "Bulk Update", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Download Backups", "link_count": 0, "link_to": "backups", "link_type": "Page", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Deleted Documents", "link_count": 0, "link_to": "Deleted Document", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Email", "link_count": 4, "link_type": "DocType", "onboard": 0, "type": "Card Break"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON><PERSON> Account", "link_count": 0, "link_to": "<PERSON><PERSON> Account", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Email Domain", "link_count": 0, "link_to": "Email Domain", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "<PERSON>ail Te<PERSON>late", "link_count": 0, "link_to": "<PERSON>ail Te<PERSON>late", "link_type": "DocType", "onboard": 0, "type": "Link"}, {"hidden": 0, "is_query_report": 0, "label": "Email Group", "link_count": 0, "link_to": "Email Group", "link_type": "DocType", "onboard": 0, "type": "Link"}], "modified": "2025-06-27 11:39:44.392114", "modified_by": "Administrator", "module": "Automation", "name": "Tools", "number_cards": [], "owner": "Administrator", "parent_page": "", "public": 1, "quick_lists": [], "restrict_to_domain": "", "roles": [], "sequence_id": 1.0, "shortcuts": [{"color": "Grey", "doc_view": "List", "label": "Import Data", "link_to": "Data Import", "type": "DocType"}, {"doc_view": "", "label": "ToDo", "link_to": "ToDo", "stats_filter": "[[\"ToDo\",\"status\",\"=\",\"Open\",false]]", "type": "DocType"}, {"label": "File", "link_to": "File", "type": "DocType"}, {"label": "Assignment Rule", "link_to": "Assignment Rule", "type": "DocType"}], "title": "Tools"}
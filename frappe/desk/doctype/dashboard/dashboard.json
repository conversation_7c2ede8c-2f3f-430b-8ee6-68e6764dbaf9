{"actions": [], "allow_rename": 1, "autoname": "field:dashboard_name", "creation": "2019-01-10 12:54:40.938705", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["dashboard_name", "is_default", "is_standard", "module", "charts", "chart_options", "cards"], "fields": [{"fieldname": "dashboard_name", "fieldtype": "Data", "in_list_view": 1, "label": "Dashboard Name", "reqd": 1, "unique": 1}, {"default": "0", "fieldname": "is_default", "fieldtype": "Check", "label": "<PERSON>"}, {"fieldname": "charts", "fieldtype": "Table", "label": "Charts", "options": "Dashboard Chart Link", "reqd": 1}, {"description": "Set Default Options for all charts on this Dashboard (Ex: \"colors\": [\"#d1d8dd\", \"#ff5858\"])", "fieldname": "chart_options", "fieldtype": "Code", "label": "Chart Options", "options": "JSON"}, {"fieldname": "cards", "fieldtype": "Table", "label": "Cards", "options": "Number Card Link"}, {"default": "0", "fieldname": "is_standard", "fieldtype": "Check", "label": "Is Standard", "read_only_depends_on": "eval: !frappe.boot.developer_mode"}, {"depends_on": "eval: doc.is_standard", "fieldname": "module", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON>", "mandatory_depends_on": "eval: doc.is_standard", "options": "<PERSON><PERSON><PERSON>"}], "links": [], "modified": "2024-03-23 16:02:16.071715", "modified_by": "Administrator", "module": "Desk", "name": "Dashboard", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Dashboard Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Desk User", "share": 1}], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "dashboard_name", "track_changes": 1}
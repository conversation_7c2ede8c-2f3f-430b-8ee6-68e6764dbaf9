{"actions": [], "autoname": "Prompt", "creation": "2019-10-23 15:00:48.392374", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["disable_count", "disable_auto_refresh", "disable_sidebar_stats", "disable_automatic_recency_filters", "column_break_oany", "disable_comment_count", "disable_scrolling", "allow_edit", "section_break_evqq", "fields_html", "fields"], "fields": [{"default": "0", "fieldname": "disable_count", "fieldtype": "Check", "label": "Disable Count"}, {"default": "0", "fieldname": "disable_sidebar_stats", "fieldtype": "Check", "label": "Disable Sidebar Stats"}, {"default": "0", "fieldname": "disable_auto_refresh", "fieldtype": "Check", "label": "Disable Auto Refresh"}, {"fieldname": "fields_html", "fieldtype": "HTML", "label": "Fields"}, {"fieldname": "fields", "fieldtype": "Code", "hidden": 1, "label": "Fields", "read_only": 1}, {"default": "0", "fieldname": "disable_comment_count", "fieldtype": "Check", "label": "Disable Comment Count"}, {"default": "0", "description": "Allow editing even if the doctype has a workflow set up.\n\nDoes nothing if a workflow isn't set up.", "fieldname": "allow_edit", "fieldtype": "Check", "label": "Allow Bulk Editing"}, {"default": "0", "fieldname": "disable_automatic_recency_filters", "fieldtype": "Check", "label": "Disable Automatic Recency Filters"}, {"default": "0", "fieldname": "disable_scrolling", "fieldtype": "Check", "label": "Disable Scrolling"}, {"fieldname": "column_break_oany", "fieldtype": "Column Break"}, {"fieldname": "section_break_evqq", "fieldtype": "Section Break"}], "grid_page_length": 50, "links": [], "modified": "2025-08-25 15:54:18.886680", "modified_by": "Administrator", "module": "Desk", "name": "List View Settings", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "read_only": 1, "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}
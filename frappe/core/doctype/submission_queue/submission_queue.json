{"actions": [], "autoname": "hash", "beta": 1, "creation": "2022-10-04 00:41:00.028163", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["status", "created_at", "enqueued_by", "job_id", "column_break_5", "ended_at", "ref_doctype", "ref_docname", "section_break_8", "exception"], "fields": [{"fieldname": "job_id", "fieldtype": "Link", "label": "Job Id", "options": "RQ Job", "read_only": 1}, {"fieldname": "ref_doctype", "fieldtype": "Link", "in_list_view": 1, "label": "Reference DocType", "options": "DocType", "read_only": 1}, {"fieldname": "ref_docname", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Reference Docname", "options": "ref_doctype", "read_only": 1, "search_index": 1}, {"fieldname": "status", "fieldtype": "Select", "hidden": 1, "in_list_view": 1, "label": "Status", "options": "Queued\nFinished\nFailed", "read_only": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "section_break_8", "fieldtype": "Section Break"}, {"fieldname": "enqueued_by", "fieldtype": "Data", "is_virtual": 1, "label": "Enqueued By", "read_only": 1}, {"fieldname": "ended_at", "fieldtype": "Datetime", "label": "Ended At", "read_only": 1}, {"fieldname": "created_at", "fieldtype": "Datetime", "is_virtual": 1, "label": "Created At", "read_only": 1}, {"fieldname": "exception", "fieldtype": "Long Text", "label": "Exception", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-03-23 16:03:39.111282", "modified_by": "Administrator", "module": "Core", "name": "Submission Queue", "naming_rule": "Random", "owner": "Administrator", "permissions": [{"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1}, {"if_owner": 1, "read": 1, "role": "Desk User"}], "sort_field": "creation", "sort_order": "DESC", "states": [{"color": "Blue", "title": "Queued"}, {"color": "Red", "title": "Failed"}, {"color": "Green", "title": "Finished"}]}
{"actions": [], "allow_rename": 1, "autoname": "format:Package Import at {creation}", "creation": "2021-09-05 16:36:46.680094", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["attach_package", "activate", "force", "log"], "fields": [{"fieldname": "attach_package", "fieldtype": "Attach", "label": "Attach Package"}, {"default": "0", "fieldname": "activate", "fieldtype": "Check", "label": "Activate"}, {"fieldname": "log", "fieldtype": "Code", "label": "Log", "read_only": 1}, {"default": "0", "fieldname": "force", "fieldtype": "Check", "label": "Force"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-03-23 16:03:33.422131", "modified_by": "Administrator", "module": "Core", "name": "Package Import", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}
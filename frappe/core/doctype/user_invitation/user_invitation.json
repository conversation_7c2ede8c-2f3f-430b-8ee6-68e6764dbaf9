{"actions": [], "allow_rename": 1, "creation": "2025-07-07 14:19:31.014655", "doctype": "DocType", "engine": "InnoDB", "field_order": ["email", "app_name", "redirect_to_path", "roles", "status", "invited_by", "key", "user", "email_sent_at", "accepted_at"], "fields": [{"fieldname": "email", "fieldtype": "Data", "in_list_view": 1, "label": "Email", "reqd": 1, "set_only_once": 1}, {"fieldname": "invited_by", "fieldtype": "Link", "hidden": 1, "in_list_view": 1, "label": "Invited By", "options": "User", "read_only": 1}, {"default": "Pending", "fieldname": "status", "fieldtype": "Select", "hidden": 1, "in_list_view": 1, "label": "Status", "options": "Pending\nAccepted\nEx<PERSON>\nCancelled", "read_only": 1}, {"fieldname": "email_sent_at", "fieldtype": "Datetime", "hidden": 1, "label": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "accepted_at", "fieldtype": "Datetime", "hidden": 1, "label": "Accepted At", "read_only": 1}, {"fieldname": "user", "fieldtype": "Link", "hidden": 1, "label": "User", "options": "User", "read_only": 1}, {"fieldname": "app_name", "fieldtype": "Select", "in_list_view": 1, "label": "App Name", "reqd": 1, "set_only_once": 1}, {"fieldname": "redirect_to_path", "fieldtype": "Data", "label": "Redirect To Path", "read_only_depends_on": "eval:doc.status!==\"Pending\"", "reqd": 1}, {"fieldname": "key", "fieldtype": "Data", "hidden": 1, "label": "Key", "read_only": 1}, {"fieldname": "roles", "fieldtype": "Table MultiSelect", "label": "Roles", "options": "User Role", "read_only_depends_on": "eval:<PERSON><PERSON><PERSON>(doc.creation)", "reqd": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-26 11:52:46.984800", "modified_by": "Administrator", "module": "Core", "name": "User Invitation", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": [{"color": "Green", "title": "Accepted"}, {"color": "Orange", "title": "Pending"}, {"color": "Yellow", "title": "Expired"}, {"color": "Red", "title": "Cancelled"}]}
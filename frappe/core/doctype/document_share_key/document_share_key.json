{"actions": [], "allow_rename": 1, "autoname": "hash", "creation": "2022-01-14 13:40:49.487646", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["reference_doctype", "reference_docname", "key", "expires_on"], "fields": [{"fieldname": "reference_doctype", "fieldtype": "Link", "label": "Reference Document Type", "options": "DocType", "read_only": 1, "search_index": 1}, {"fieldname": "reference_docname", "fieldtype": "Dynamic Link", "label": "Reference Document Name", "options": "reference_doctype", "read_only": 1, "search_index": 1}, {"fieldname": "key", "fieldtype": "Data", "label": "Key", "read_only": 1}, {"fieldname": "expires_on", "fieldtype": "Date", "in_list_view": 1, "label": "Expires On", "read_only": 1}], "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2024-03-23 16:03:22.892995", "modified_by": "Administrator", "module": "Core", "name": "Document Share Key", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "read_only": 1, "sort_field": "creation", "sort_order": "DESC", "states": []}
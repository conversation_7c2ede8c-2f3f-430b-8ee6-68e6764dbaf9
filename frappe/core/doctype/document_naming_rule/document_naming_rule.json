{"actions": [], "creation": "2020-09-07 12:48:48.334318", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["document_type", "disabled", "priority", "section_break_3", "conditions", "naming_section", "prefix", "counter", "column_break_xfqa", "prefix_digits"], "fields": [{"fieldname": "document_type", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Document Type", "options": "DocType", "reqd": 1}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "prefix", "fieldtype": "Data", "in_list_view": 1, "label": "Prefix", "mandatory_depends_on": "eval:doc.naming_by===\"Numbered\"", "reqd": 1}, {"default": "0", "description": "Warning: Updating counter may lead to document name conflicts if not done properly", "fieldname": "counter", "fieldtype": "Int", "in_list_view": 1, "label": "Counter", "no_copy": 1}, {"default": "5", "description": "Example: 00001", "fieldname": "prefix_digits", "fieldtype": "Int", "label": "Digits", "mandatory_depends_on": "eval:doc.naming_by===\"Numbered\"", "reqd": 1}, {"fieldname": "naming_section", "fieldtype": "Section Break", "label": "Naming"}, {"collapsible": 1, "collapsible_depends_on": "conditions", "fieldname": "section_break_3", "fieldtype": "Section Break", "label": "Rule Conditions"}, {"fieldname": "conditions", "fieldtype": "Table", "label": "Conditions", "options": "Document Naming Rule Condition"}, {"description": "Rules with higher priority number will be applied first.", "fieldname": "priority", "fieldtype": "Int", "in_standard_filter": 1, "label": "Priority"}, {"fieldname": "column_break_xfqa", "fieldtype": "Column Break"}], "index_web_pages_for_search": 1, "links": [], "modified": "2023-11-21 11:58:25.712375", "modified_by": "Administrator", "module": "Core", "name": "Document Naming Rule", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "priority", "sort_order": "DESC", "states": [], "title_field": "document_type", "track_changes": 1}
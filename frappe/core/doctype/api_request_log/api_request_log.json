{"actions": [], "allow_rename": 1, "creation": "2025-05-21 16:51:56.070193", "doctype": "DocType", "engine": "InnoDB", "field_order": ["path", "method", "user"], "fields": [{"fieldname": "path", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Path"}, {"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "User", "options": "User"}, {"fieldname": "method", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Method"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-05-21 17:09:55.054044", "modified_by": "Administrator", "module": "Core", "name": "API Request Log", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}
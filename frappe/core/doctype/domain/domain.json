{"actions": [], "autoname": "field:domain", "creation": "2017-05-03 15:07:39.752820", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["domain"], "fields": [{"fieldname": "domain", "fieldtype": "Data", "in_list_view": 1, "label": "Domain", "reqd": 1, "unique": 1}], "links": [], "modified": "2024-03-23 16:03:22.994251", "modified_by": "Administrator", "module": "Core", "name": "Domain", "owner": "Administrator", "permissions": [{"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Administrator", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "search_fields": "domain", "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "domain"}
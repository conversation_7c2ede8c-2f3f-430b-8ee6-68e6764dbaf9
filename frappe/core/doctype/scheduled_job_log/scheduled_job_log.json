{"actions": [], "creation": "2019-09-23 14:36:36.935869", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["status", "scheduled_job_type", "details", "debug_log"], "fields": [{"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Scheduled\nComplete\nFailed", "read_only": 1, "reqd": 1}, {"fieldname": "details", "fieldtype": "Code", "label": "Details", "read_only": 1}, {"fieldname": "scheduled_job_type", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Scheduled Job", "options": "Scheduled Job Type", "read_only": 1, "reqd": 1}, {"fieldname": "debug_log", "fieldtype": "Code", "label": "Debug Log", "read_only": 1}], "links": [], "modified": "2024-03-23 16:03:37.828186", "modified_by": "Administrator", "module": "Core", "name": "Scheduled Job Log", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "scheduled_job_type"}
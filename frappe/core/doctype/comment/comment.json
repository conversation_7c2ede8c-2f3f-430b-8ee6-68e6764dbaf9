{"actions": [], "creation": "2019-02-07 10:10:46.845678", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["comment_type", "comment_email", "subject", "comment_by", "published", "seen", "column_break_5", "reference_doctype", "reference_name", "reference_owner", "section_break_10", "content", "ip_address"], "fields": [{"default": "Comment", "fieldname": "comment_type", "fieldtype": "Select", "label": "Comment Type", "options": "Comment\nLike\nInfo\nLabel\nWorkflow\nCreated\nSubmitted\nCancelled\nUpdated\nDeleted\nAssigned\nAssignment Completed\nAttachment\nAttachment Removed\nShared\nUnshared\nBot\nRelinked\nEdit", "reqd": 1}, {"fieldname": "comment_email", "fieldtype": "Data", "in_list_view": 1, "label": "Comment <PERSON>"}, {"fieldname": "subject", "fieldtype": "Text", "label": "Subject"}, {"fieldname": "comment_by", "fieldtype": "Data", "in_list_view": 1, "label": "Comment By"}, {"default": "0", "fieldname": "published", "fieldtype": "Check", "label": "Published"}, {"default": "0", "fieldname": "seen", "fieldtype": "Check", "label": "Seen"}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "reference_doctype", "fieldtype": "Link", "in_list_view": 1, "label": "Reference Document Type", "options": "DocType"}, {"fieldname": "reference_name", "fieldtype": "Dynamic Link", "label": "Reference Name", "options": "reference_doctype"}, {"fieldname": "reference_owner", "fieldtype": "Data", "label": "Reference Owner", "read_only": 1}, {"fieldname": "section_break_10", "fieldtype": "Section Break"}, {"fieldname": "content", "fieldtype": "HTML Editor", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Content"}, {"fieldname": "ip_address", "fieldtype": "Data", "hidden": 1, "label": "IP Address"}], "links": [], "modified": "2024-08-14 16:10:15.413830", "modified_by": "Administrator", "module": "Core", "name": "Comment", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Website Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "comment_type", "track_changes": 1}
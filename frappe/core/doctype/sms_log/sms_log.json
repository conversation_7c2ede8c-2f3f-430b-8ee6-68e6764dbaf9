{"actions": [], "autoname": "SYS-SMS-.#####", "creation": "2012-03-27 14:36:47", "doctype": "DocType", "engine": "InnoDB", "field_order": ["sender_name", "sent_on", "column_break0", "message", "sec_break1", "no_of_requested_sms", "requested_numbers", "column_break1", "no_of_sent_sms", "sent_to"], "fields": [{"fieldname": "sender_name", "fieldtype": "Data", "label": "Sender Name", "read_only": 1}, {"fieldname": "sent_on", "fieldtype": "Date", "label": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "column_break0", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "message", "fieldtype": "Small Text", "label": "Message", "read_only": 1}, {"fieldname": "sec_break1", "fieldtype": "Section Break", "options": "Simple"}, {"fieldname": "no_of_requested_sms", "fieldtype": "Int", "label": "No of Requested SMS", "read_only": 1}, {"fieldname": "requested_numbers", "fieldtype": "Code", "label": "Requested Numbers", "read_only": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "width": "50%"}, {"fieldname": "no_of_sent_sms", "fieldtype": "Int", "label": "No of Sent SMS", "read_only": 1}, {"fieldname": "sent_to", "fieldtype": "Code", "label": "<PERSON><PERSON>", "read_only": 1}], "icon": "fa fa-mobile-phone", "idx": 1, "links": [], "modified": "2024-03-23 16:03:38.537041", "modified_by": "Administrator", "module": "Core", "name": "SMS Log", "owner": "Administrator", "permissions": [{"email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager"}], "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}
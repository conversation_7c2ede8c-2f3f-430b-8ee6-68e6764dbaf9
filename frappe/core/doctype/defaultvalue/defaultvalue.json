{"actions": [], "autoname": "hash", "creation": "2013-02-22 01:27:32", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["defkey", "defvalue"], "fields": [{"fieldname": "defkey", "fieldtype": "Data", "in_list_view": 1, "label": "Key", "oldfieldname": "defkey", "oldfieldtype": "Data", "print_width": "200px", "reqd": 1, "width": "200px"}, {"fieldname": "defvalue", "fieldtype": "Text", "in_list_view": 1, "label": "Value", "oldfieldname": "defvalue", "oldfieldtype": "Text", "print_width": "200px", "width": "200px"}], "idx": 1, "istable": 1, "links": [], "modified": "2024-03-23 16:02:17.533639", "modified_by": "Administrator", "module": "Core", "name": "DefaultValue", "owner": "Administrator", "permissions": [], "sort_field": "creation", "sort_order": "DESC", "states": []}
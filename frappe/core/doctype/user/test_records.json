[{"doctype": "User", "email": "<EMAIL>", "enabled": 1, "first_name": "_Test", "new_password": "Eastern_43A1W", "roles": [{"doctype": "Has Role", "parentfield": "roles", "role": "_Test Role"}, {"doctype": "Has Role", "parentfield": "roles", "role": "System Manager"}]}, {"doctype": "User", "email": "<EMAIL>", "first_name": "_Test1", "new_password": "Eastern_43A1W"}, {"doctype": "User", "email": "<EMAIL>", "first_name": "_Test2", "new_password": "Eastern_43A1W", "enabled": 1}, {"doctype": "User", "email": "<EMAIL>", "first_name": "_Test3", "new_password": "Eastern_43A1W", "enabled": 1}, {"doctype": "User", "email": "<EMAIL>", "first_name": "_Test4", "new_password": "Eastern_43A1W", "enabled": 1}, {"doctype": "User", "email": "test'<EMAIL>", "first_name": "_Test'5", "new_password": "Eastern_43A1W", "enabled": 1}, {"doctype": "User", "email": "<EMAIL>", "first_name": "_Test Perm", "new_password": "Eastern_43A1W", "enabled": 1}, {"doctype": "User", "email": "<EMAIL>", "enabled": 1, "first_name": "_Test", "new_password": "Eastern_43A1W", "roles": [{"doctype": "Has Role", "parentfield": "roles", "role": "_Test Role 2"}, {"doctype": "Has Role", "parentfield": "roles", "role": "System Manager"}]}, {"doctype": "User", "email": "<EMAIL>", "enabled": 1, "first_name": "_Test", "new_password": "Eastern_43A1W", "roles": [{"doctype": "Has Role", "parentfield": "roles", "role": "System Manager"}]}]
{"actions": [], "creation": "2019-09-23 16:28:13.953520", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["label", "action_type", "action", "group", "hidden", "custom"], "fields": [{"columns": 2, "fieldname": "label", "fieldtype": "Data", "in_list_view": 1, "label": "Label", "reqd": 1}, {"fieldname": "group", "fieldtype": "Data", "in_list_view": 1, "label": "Group"}, {"columns": 2, "fieldname": "action_type", "fieldtype": "Select", "in_list_view": 1, "label": "Action Type", "options": "Server Action\nRoute", "reqd": 1}, {"columns": 4, "fieldname": "action", "fieldtype": "Small Text", "in_list_view": 1, "label": "Action / Route", "reqd": 1}, {"default": "0", "fieldname": "hidden", "fieldtype": "Check", "label": "Hidden"}, {"default": "0", "fieldname": "custom", "fieldtype": "Check", "hidden": 1, "label": "Custom"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-03-23 16:03:21.873210", "modified_by": "Administrator", "module": "Core", "name": "DocType Action", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}
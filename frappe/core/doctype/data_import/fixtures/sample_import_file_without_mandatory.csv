Title  ,Description        ,Number ,another_number ,ID (Table Field 1) ,Child Title (Table Field 1) ,Child Description (Table Field 1) ,Child 2 Title (Table Field 2) ,Child 2 Date (Table Field 2) ,Child 2 Number (Table Field 2) ,Child Title (Table Field 1 Again) ,Child Date (Table Field 1 Again) ,Child Number (Table Field 1 Again) ,table_field_1_again.child_another_number
Test 5  ,test description   ,1      ,2              ,""                 ,                            ,child description                 ,child title                   ,14-08-2019                   ,4                              ,child title again                 ,22-09-2020                       ,5                                  , 7
       ,                   ,       ,               ,                   ,child title 2               ,child description 2               ,title child                   ,30-10-2019                   ,5                              ,                                  ,22-09-2021                       ,                                   ,
       ,test description 2 ,1      ,2              ,                   ,child mandatory title       ,                                  ,title child man               ,                             ,                               ,child mandatory again             ,                                 ,                                   ,
Test 4 ,test description 3 ,4      ,5              ,""                 ,child title asdf            ,child description asdf            ,child title asdf adsf         ,15-08-2019                   ,6                              ,child title again asdf            ,22-09-2022                       ,9                                  , 71

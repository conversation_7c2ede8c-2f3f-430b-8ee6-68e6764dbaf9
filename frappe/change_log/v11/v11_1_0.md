- Dynamic [Frappe Charts](https://github.com/frappe/charts) with Report Builder (built by @pratu16x7)
- New Frappe Chat for easier internal communication (built by @achillesrasquinha)
- [Frappe DataTable](https://github.com/frappe/datatable) for better reports (built by @netchampfaris)
- Google Calendar can now be integrated with Frappe
- Files can now be uploaded using drag-and-drop
- Enhanced List View and Tree View
- Bulk Actions from List View
- Quill editor has been introduced in place of Summernote
- HTML Editor has been introduced
- New User Permissions
- Subscriptions in ERPNext now moved to Auto Repeat in Frappe
- Support for Razorpay and PayPal subscriptions
- Better Social login, Workflow
- Messages for when user goes online/offline
- Logout from all sessions on password change
- Desktop icons can now be selected from a dialog box
- Changes have been made to ensure Frappe is compatible with Python 3
- Better documentation is now available with support for more languages
- A lot of other fixes have been done to ensure a better overall user experience
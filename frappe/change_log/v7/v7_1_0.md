#### Gantt View
- New Gantt view for documents where date range is available

#### In-App Help
- Search for help from within the app. Click on "Help"

#### Web Form
- Add grids (child tables)
- Add page breaks (for long forms)
- Add payment gateway
- Add attachments

#### Auto Email Report
- Email reports automatically on daily / weekly / monthly basis

#### Other Fixes
- Send a popup to all users on login for a new Note by checking on "Notify users with a popup when they log in"
- Portal Users (Customers, Supplier, Students) can now have roles
- Sidebar in portal view will be rendered as per roles and can be configured from Portal Settings
- Restrict the number of backups to be saved in System Settings
- Scheduler log is now error log and as MyISAM
- A better way to export customzations and Email Alert directly from Customize Form
- Option to send email from Data Import Tool where applicable
- Integration Broker
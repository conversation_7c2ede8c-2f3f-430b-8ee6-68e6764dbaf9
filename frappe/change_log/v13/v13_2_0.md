# Version 13.2.0 Release Notes

### Features & Enhancements

- Add option to mention a group of users ([#12844](https://github.com/frappe/frappe/pull/12844))
- Copy DocType / documents across sites ([#12872](https://github.com/frappe/frappe/pull/12872))
- Scheduler log in notifications ([#1135](https://github.com/frappe/frappe/pull/1135))
- Add Enable/Disable Webhook via Check Field ([#12842](https://github.com/frappe/frappe/pull/12842))
- Allow query/custom reports to save custom data in the json field ([#12534](https://github.com/frappe/frappe/pull/12534))

### Fixes

- Load server translations in boot (backport #12848) ([#12852](https://github.com/frappe/frappe/pull/12852))
- Allow to override dashboard chart properties type/color ([#12846](https://github.com/frappe/frappe/pull/12846))
- Multi-column paste in grid ([#12861](https://github.com/frappe/frappe/pull/12861))
- Add log_error and FrappeClient to restricted python ([#12857](https://github.com/frappe/frappe/pull/12857))
- Redirect Web Form user directly to success URL, if no amount is due ([#12661](https://github.com/frappe/frappe/pull/12661))
- Attachment pill lock icon redirects to File ([#12864](https://github.com/frappe/frappe/pull/12864))
- Redirect Web Form user directly to success URL, if no amount is due (backport #12661) ([#12856](https://github.com/frappe/frappe/pull/12856))
- Remove events to redraw charts ([#12973](https://github.com/frappe/frappe/pull/12973))
- Don't allow user to remove/change data source file in data import ([#12827](https://github.com/frappe/frappe/pull/12827))
- Load server translations in boot ([#12848](https://github.com/frappe/frappe/pull/12848))
- Newly created Workspace not being accessible unless a shortcut u… ([#12866](https://github.com/frappe/frappe/pull/12866))
- Currency labels in grids ([#12974](https://github.com/frappe/frappe/pull/12974))
- Handle error while session start ([#12933](https://github.com/frappe/frappe/pull/12933))
- Add field type check in custom field validation ([#12858](https://github.com/frappe/frappe/pull/12858))
- Make language select optional and fix breakpoint issues ([#12860](https://github.com/frappe/frappe/pull/12860))
- Form Dashboard reference link ([#12945](https://github.com/frappe/frappe/pull/12945))
- Invalid HTML generated by the base template ([#12953](https://github.com/frappe/frappe/pull/12953))
- Default values were not triggering change event ([#12975](https://github.com/frappe/frappe/pull/12975))
- Make strings translatable ([#12877](https://github.com/frappe/frappe/pull/12877))
- Added build-message-files command ([#12950](https://github.com/frappe/frappe/pull/12950))
# Version 13.1.0 Release Notes

### Features & Enhancements

- Automated mail notifications will be shown in timeline ([#12693](https://github.com/frappe/frappe/pull/12693))
- Introduced Client Script for List views ([#12590](https://github.com/frappe/frappe/pull/12590))
- Introduced language switcher for guest users on website navbar ([#12813](https://github.com/frappe/frappe/pull/12813))
- Option to give submit permission while sharing a document ([#12799](https://github.com/frappe/frappe/pull/12799))
- Added option to set `autoname` in Customize Form ([#12413](https://github.com/frappe/frappe/pull/12413))
- Virtual DocType ([#12121](https://github.com/frappe/frappe/pull/12121))

### Fixes

- Workspace fixes ([#12650](https://github.com/frappe/frappe/pull/12650)) ([#12655](https://github.com/frappe/frappe/pull/12655)) ([#12869](https://github.com/frappe/frappe/pull/12869))
- Fixed an issue where select options were not getting updated in Grid ([#12839](https://github.com/frappe/frappe/pull/12839))
- Webform Fixes ([#12630](https://github.com/frappe/frappe/pull/12630)) ([#12756](https://github.com/frappe/frappe/pull/12756)) ([#12819](https://github.com/frappe/frappe/pull/12819))
- Fixed timespan filter for next and last timespans ([#12509](https://github.com/frappe/frappe/pull/12509))
- System Notification fixes ([#12719](https://github.com/frappe/frappe/pull/12719))
- Design Fixes ([#12669](https://github.com/frappe/frappe/pull/12669)) ([#12591](https://github.com/frappe/frappe/pull/12591)) ([#12557](https://github.com/frappe/frappe/pull/12557)) ([#12751](https://github.com/frappe/frappe/pull/12751)) ([#12864](https://github.com/frappe/frappe/pull/12864))
- Fixed Multi-column paste in grid ([#12861](https://github.com/frappe/frappe/pull/12861))
- Fixed grid validation ([#12744](https://github.com/frappe/frappe/pull/12744))
- Fixed currency value formatting in dashboard chart ([#12613](https://github.com/frappe/frappe/pull/12613))

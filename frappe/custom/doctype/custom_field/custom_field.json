{"actions": [], "allow_import": 1, "creation": "2024-02-21 18:14:42.281748", "description": "Adds a custom field to a DocType", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["is_system_generated", "dt", "module", "label", "placeholder", "label_help", "fieldname", "insert_after", "length", "link_filters", "column_break_6", "fieldtype", "precision", "hide_seconds", "hide_days", "options", "sort_options", "fetch_from", "fetch_if_empty", "options_help", "section_break_11", "collapsible", "collapsible_depends_on", "default", "depends_on", "mandatory_depends_on", "read_only_depends_on", "properties", "non_negative", "reqd", "unique", "is_virtual", "read_only", "ignore_user_permissions", "hidden", "print_hide", "print_hide_if_no_value", "print_width", "no_copy", "allow_on_submit", "in_list_view", "in_standard_filter", "in_global_search", "in_preview", "bold", "report_hide", "search_index", "allow_in_quick_entry", "ignore_xss_filter", "translatable", "hide_border", "show_dashboard", "description", "permlevel", "width", "columns"], "fields": [{"bold": 1, "fieldname": "dt", "fieldtype": "Link", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "DocType", "oldfieldname": "dt", "oldfieldtype": "Link", "options": "DocType", "reqd": 1, "search_index": 1}, {"bold": 1, "fieldname": "label", "fieldtype": "Data", "in_filter": 1, "label": "Label", "no_copy": 1, "oldfieldname": "label", "oldfieldtype": "Data"}, {"fieldname": "label_help", "fieldtype": "HTML", "label": "Label Help", "oldfieldtype": "HTML"}, {"fieldname": "fieldname", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Fieldname", "no_copy": 1, "oldfieldname": "fieldname", "oldfieldtype": "Data", "read_only": 1}, {"description": "Select the label after which you want to insert new field.", "fieldname": "insert_after", "fieldtype": "Select", "label": "Insert After", "no_copy": 1, "oldfieldname": "insert_after", "oldfieldtype": "Select"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"bold": 1, "default": "Data", "fieldname": "fieldtype", "fieldtype": "Select", "in_filter": 1, "in_list_view": 1, "label": "Field Type", "oldfieldname": "fieldtype", "oldfieldtype": "Select", "options": "Autocomplete\nAttach\nAttach Image\nBarcode\nButton\nCheck\nCode\nColor\nColumn Break\nCurrency\nData\nDate\nDatetime\nDuration\nDynamic Link\nFloat\nFold\nGeolocation\nHeading\nHTML\nHTML Editor\nIcon\nImage\nInt\nJSON\nLink\nLong Text\nMarkdown Editor\nPassword\nPercent\nPhone\nRead Only\nRating\nSection Break\nSelect\nSignature\nSmall Text\nTab Break\nTable\nTable MultiSelect\nText\nText Editor\nTime", "reqd": 1, "sort_options": 1}, {"depends_on": "eval:in_list([\"Float\", \"Currency\", \"Percent\"], doc.fieldtype)", "description": "Set non-standard precision for a Float or Currency field", "fieldname": "precision", "fieldtype": "Select", "label": "Precision", "options": "\n0\n1\n2\n3\n4\n5\n6\n7\n8\n9"}, {"fieldname": "options", "fieldtype": "Small Text", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Options", "oldfieldname": "options", "oldfieldtype": "Text"}, {"fieldname": "fetch_from", "fieldtype": "Small Text", "label": "<PERSON>tch From"}, {"default": "0", "description": "If unchecked, the value will always be re-fetched on save.", "fieldname": "fetch_if_empty", "fieldtype": "Check", "label": "Fetch on Save if Empty"}, {"fieldname": "options_help", "fieldtype": "HTML", "label": "Options Help", "oldfieldtype": "HTML"}, {"fieldname": "section_break_11", "fieldtype": "Section Break"}, {"default": "0", "depends_on": "eval:doc.fieldtype==\"Section Break\"", "fieldname": "collapsible", "fieldtype": "Check", "label": "Collapsible"}, {"depends_on": "eval:doc.fieldtype==\"Section Break\"", "fieldname": "collapsible_depends_on", "fieldtype": "Code", "label": "Collapsible Depends On"}, {"fieldname": "default", "fieldtype": "Text", "ignore_xss_filter": 1, "label": "Default Value", "oldfieldname": "default", "oldfieldtype": "Text"}, {"fieldname": "depends_on", "fieldtype": "Code", "label": "Depends On", "length": 255}, {"fieldname": "description", "fieldtype": "Text", "label": "Field Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "300px", "width": "300px"}, {"default": "0", "fieldname": "permlevel", "fieldtype": "Int", "label": "Permission Level", "oldfieldname": "permlevel", "oldfieldtype": "Int"}, {"fieldname": "width", "fieldtype": "Data", "label": "<PERSON><PERSON><PERSON>", "oldfieldname": "width", "oldfieldtype": "Data"}, {"description": "Number of columns for a field in a List View or a Grid (Total Columns should be less than 11)", "fieldname": "columns", "fieldtype": "Int", "label": "Columns"}, {"fieldname": "properties", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"default": "0", "fieldname": "reqd", "fieldtype": "Check", "in_list_view": 1, "label": "Is Mandatory Field", "oldfieldname": "reqd", "oldfieldtype": "Check"}, {"default": "0", "fieldname": "unique", "fieldtype": "Check", "label": "Unique"}, {"default": "0", "fieldname": "is_virtual", "fieldtype": "Check", "label": "Is Virtual"}, {"default": "0", "fieldname": "read_only", "fieldtype": "Check", "label": "Read Only"}, {"default": "0", "depends_on": "eval:doc.fieldtype===\"Link\"", "fieldname": "ignore_user_permissions", "fieldtype": "Check", "label": "Ignore User Permissions"}, {"default": "0", "fieldname": "hidden", "fieldtype": "Check", "label": "Hidden"}, {"default": "0", "fieldname": "print_hide", "fieldtype": "Check", "label": "Print Hide", "oldfieldname": "print_hide", "oldfieldtype": "Check"}, {"default": "0", "depends_on": "eval:[\"Int\", \"Float\", \"Currency\", \"Percent\"].indexOf(doc.fieldtype)!==-1", "fieldname": "print_hide_if_no_value", "fieldtype": "Check", "label": "Print Hide If No Value"}, {"fieldname": "print_width", "fieldtype": "Data", "hidden": 1, "label": "Print Width", "no_copy": 1, "print_hide": 1}, {"default": "0", "fieldname": "no_copy", "fieldtype": "Check", "label": "No Copy", "oldfieldname": "no_copy", "oldfieldtype": "Check"}, {"default": "0", "fieldname": "allow_on_submit", "fieldtype": "Check", "label": "Allow on Submit", "oldfieldname": "allow_on_submit", "oldfieldtype": "Check"}, {"default": "0", "fieldname": "in_list_view", "fieldtype": "Check", "label": "In List View"}, {"default": "0", "fieldname": "in_standard_filter", "fieldtype": "Check", "label": "In Standard Filter"}, {"default": "0", "depends_on": "eval:([\"Data\", \"Select\", \"Table\", \"Text\", \"Text Editor\", \"Link\", \"Small Text\", \"Long Text\", \"Read Only\", \"Heading\", \"Dynamic Link\"].indexOf(doc.fieldtype) !== -1)", "fieldname": "in_global_search", "fieldtype": "Check", "label": "In Global Search"}, {"default": "0", "fieldname": "bold", "fieldtype": "Check", "label": "Bold"}, {"default": "0", "fieldname": "report_hide", "fieldtype": "Check", "label": "Report Hide", "oldfieldname": "report_hide", "oldfieldtype": "Check"}, {"default": "0", "fieldname": "search_index", "fieldtype": "Check", "hidden": 1, "label": "Index", "no_copy": 1, "print_hide": 1}, {"default": "0", "description": "Don't HTML Encode HTML tags like &lt;script&gt; or just characters like &lt; or &gt;, as they could be intentionally used in this field", "fieldname": "ignore_xss_filter", "fieldtype": "Check", "label": "Ignore XSS Filter"}, {"default": "0", "depends_on": "eval:['Data', 'Select', 'Text', 'Small Text', 'Text Editor'].includes(doc.fieldtype)", "fieldname": "translatable", "fieldtype": "Check", "label": "Translatable"}, {"depends_on": "eval:in_list(['<PERSON>', '<PERSON>', 'Dynamic Link', 'Password', 'Select', 'Read Only', 'Attach', 'Attach Image', 'Int'], doc.fieldtype)", "fieldname": "length", "fieldtype": "Int", "label": "Length"}, {"fieldname": "mandatory_depends_on", "fieldtype": "Code", "label": "Mandatory Depends On", "length": 255}, {"fieldname": "read_only_depends_on", "fieldtype": "Code", "label": "Read Only Depends On", "length": 255}, {"default": "0", "fieldname": "allow_in_quick_entry", "fieldtype": "Check", "label": "Allow in Quick Entry"}, {"default": "0", "depends_on": "eval:!in_list(['Table', 'Table MultiSelect'], doc.fieldtype);", "fieldname": "in_preview", "fieldtype": "Check", "label": "In Preview"}, {"default": "0", "depends_on": "eval:doc.fieldtype=='Duration'", "fieldname": "hide_seconds", "fieldtype": "Check", "label": "Hide Seconds"}, {"default": "0", "depends_on": "eval:doc.fieldtype=='Duration'", "fieldname": "hide_days", "fieldtype": "Check", "label": "Hide Days"}, {"default": "0", "depends_on": "eval:doc.fieldtype=='Section Break'", "fieldname": "hide_border", "fieldtype": "Check", "label": "Hide Border"}, {"default": "0", "depends_on": "eval:in_list([\"Int\", \"Float\", \"Currency\"], doc.fieldtype)", "fieldname": "non_negative", "fieldtype": "Check", "label": "Non Negative"}, {"fieldname": "module", "fieldtype": "Link", "label": "Module (for export)", "options": "<PERSON><PERSON><PERSON>"}, {"default": "0", "fieldname": "is_system_generated", "fieldtype": "Check", "label": "Is System Generated", "read_only": 1}, {"default": "0", "depends_on": "eval: doc.fieldtype === 'Select'", "fieldname": "sort_options", "fieldtype": "Check", "label": "Sort Options"}, {"fieldname": "link_filters", "fieldtype": "JSON", "hidden": 1, "label": "<PERSON>lters"}, {"default": "0", "fieldname": "show_dashboard", "fieldtype": "Check", "label": "Show Dashboard"}, {"fieldname": "placeholder", "fieldtype": "Data", "label": "Placeholder"}], "icon": "fa fa-glass", "idx": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2024-09-28 20:19:35.935720", "modified_by": "Administrator", "module": "Custom", "name": "Custom Field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Administrator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "search_fields": "dt,label,fieldtype,options", "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}
{"actions": [], "autoname": "hash", "creation": "2013-02-22 01:27:32", "doctype": "DocType", "document_type": "Setup", "editable_grid": 1, "engine": "InnoDB", "field_order": ["is_system_generated", "label_and_type", "label", "fieldtype", "fieldname", "non_negative", "reqd", "unique", "is_virtual", "in_list_view", "in_standard_filter", "in_global_search", "in_preview", "bold", "no_copy", "allow_in_quick_entry", "translatable", "link_filters", "column_break_7", "default", "precision", "length", "options", "sort_options", "fetch_from", "fetch_if_empty", "show_dashboard", "permissions", "depends_on", "permlevel", "hidden", "read_only", "collapsible", "allow_bulk_edit", "collapsible_depends_on", "column_break_14", "ignore_user_permissions", "allow_on_submit", "report_hide", "remember_last_selected_value", "hide_border", "ignore_xss_filter", "property_depends_on_section", "mandatory_depends_on", "column_break_33", "read_only_depends_on", "display", "in_filter", "hide_seconds", "hide_days", "column_break_21", "description", "placeholder", "print_hide", "print_hide_if_no_value", "print_width", "columns", "width", "is_custom_field"], "fields": [{"fieldname": "label_and_type", "fieldtype": "Section Break", "label": "Label and Type"}, {"fieldname": "label", "fieldtype": "Data", "in_list_view": 1, "label": "Label", "oldfieldname": "label", "oldfieldtype": "Data", "search_index": 1}, {"default": "Data", "fieldname": "fieldtype", "fieldtype": "Select", "in_list_view": 1, "label": "Type", "oldfieldname": "fieldtype", "oldfieldtype": "Select", "options": "Autocomplete\nAttach\nAttach Image\nBarcode\nButton\nCheck\nCode\nColor\nColumn Break\nCurrency\nData\nDate\nDatetime\nDuration\nDynamic Link\nFloat\nFold\nGeolocation\nHeading\nHTML\nHTML Editor\nIcon\nImage\nInt\nJSON\nLink\nLong Text\nMarkdown Editor\nPassword\nPercent\nPhone\nRating\nRead Only\nSection Break\nSelect\nSignature\nSmall Text\nTab Break\nTable\nTable MultiSelect\nText\nText Editor\nTime", "reqd": 1, "search_index": 1, "sort_options": 1}, {"fieldname": "fieldname", "fieldtype": "Data", "in_list_view": 1, "label": "Name", "oldfieldname": "fieldname", "oldfieldtype": "Data", "read_only": 1, "search_index": 1}, {"default": "0", "depends_on": "eval:!in_list([\"Section Break\", \"Column Break\", \"Button\", \"HTML\"], doc.fieldtype)", "fieldname": "reqd", "fieldtype": "Check", "in_list_view": 1, "label": "Mandatory", "oldfieldname": "reqd", "oldfieldtype": "Check", "print_width": "50px", "width": "50px"}, {"default": "0", "fieldname": "unique", "fieldtype": "Check", "label": "Unique"}, {"default": "0", "fieldname": "is_virtual", "fieldtype": "Check", "label": "Is Virtual"}, {"default": "0", "depends_on": "eval:!doc.is_virtual", "fieldname": "in_list_view", "fieldtype": "Check", "label": "In List View"}, {"default": "0", "fieldname": "in_standard_filter", "fieldtype": "Check", "label": "In Standard Filter"}, {"default": "0", "depends_on": "eval:([\"Data\", \"Select\", \"Table\", \"Text\", \"Text Editor\", \"Link\", \"Small Text\", \"Long Text\", \"Read Only\", \"Heading\", \"Dynamic Link\"].indexOf(doc.fieldtype) !== -1)", "fieldname": "in_global_search", "fieldtype": "Check", "label": "In Global Search"}, {"default": "0", "fieldname": "bold", "fieldtype": "Check", "label": "Bold"}, {"default": "1", "depends_on": "eval:['Data', 'Select', 'Text', 'Small Text', 'Text Editor'].includes(doc.fieldtype)", "fieldname": "translatable", "fieldtype": "Check", "label": "Translatable"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"depends_on": "eval:in_list([\"Float\", \"Currency\", \"Percent\"], doc.fieldtype)", "description": "Set non-standard precision for a Float or Currency field", "fieldname": "precision", "fieldtype": "Select", "label": "Precision", "options": "\n0\n1\n2\n3\n4\n5\n6\n7\n8\n9"}, {"depends_on": "eval:in_list(['<PERSON>', '<PERSON>', 'Dynamic Link', 'Password', 'Select', 'Read Only', 'Attach', 'Attach Image'], doc.fieldtype)", "fieldname": "length", "fieldtype": "Int", "label": "Length"}, {"description": "For Links, enter the DocType as range.\nFor Select, enter list of Options, each on a new line.", "fieldname": "options", "fieldtype": "Small Text", "in_list_view": 1, "label": "Options", "oldfieldname": "options", "oldfieldtype": "Text"}, {"fieldname": "fetch_from", "fieldtype": "Small Text", "label": "<PERSON>tch From"}, {"default": "0", "description": "If unchecked, the value will always be re-fetched on save.", "fieldname": "fetch_if_empty", "fieldtype": "Check", "label": "Fetch on Save if Empty"}, {"fieldname": "permissions", "fieldtype": "Section Break", "label": "Permissions"}, {"description": "This field will appear only if the fieldname defined here has value OR the rules are true (examples):\nmyfield\neval:doc.myfield=='My Value'\neval:doc.age&gt;18", "fieldname": "depends_on", "fieldtype": "Code", "label": "Depends On", "oldfieldname": "depends_on", "oldfieldtype": "Data", "options": "JS"}, {"default": "0", "depends_on": "eval:!in_list(['Section Break', 'Column Break', 'Tab Break'], doc.fieldtype)", "fieldname": "permlevel", "fieldtype": "Int", "in_list_view": 1, "label": "Perm Level", "oldfieldname": "permlevel", "oldfieldtype": "Int"}, {"default": "0", "fieldname": "hidden", "fieldtype": "Check", "label": "Hidden", "oldfieldname": "hidden", "oldfieldtype": "Check", "print_width": "50px", "width": "50px"}, {"default": "0", "fieldname": "read_only", "fieldtype": "Check", "label": "Read Only"}, {"default": "0", "depends_on": "eval:doc.fieldtype==\"Section Break\"", "fieldname": "collapsible", "fieldtype": "Check", "label": "Collapsible"}, {"default": "0", "depends_on": "eval: doc.fieldtype == \"Table\"", "fieldname": "allow_bulk_edit", "fieldtype": "Check", "label": "Allow Bulk Edit"}, {"depends_on": "eval:doc.fieldtype==\"Section Break\"", "fieldname": "collapsible_depends_on", "fieldtype": "Code", "label": "Collapsible Depends On", "options": "JS"}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "ignore_user_permissions", "fieldtype": "Check", "label": "Ignore User Permissions"}, {"default": "0", "fieldname": "allow_on_submit", "fieldtype": "Check", "label": "Allow on Submit", "oldfieldname": "allow_on_submit", "oldfieldtype": "Check"}, {"default": "0", "fieldname": "report_hide", "fieldtype": "Check", "label": "Report Hide", "oldfieldname": "report_hide", "oldfieldtype": "Check"}, {"default": "0", "depends_on": "eval:(doc.fieldtype == 'Link')", "fieldname": "remember_last_selected_value", "fieldtype": "Check", "label": "Remember Last Selected Value"}, {"fieldname": "display", "fieldtype": "Section Break", "label": "Display"}, {"fieldname": "default", "fieldtype": "Small Text", "label": "<PERSON><PERSON><PERSON>", "oldfieldname": "default", "oldfieldtype": "Text"}, {"default": "0", "fieldname": "in_filter", "fieldtype": "Check", "label": "In Filter", "oldfieldname": "in_filter", "oldfieldtype": "Check", "print_width": "50px", "width": "50px"}, {"fieldname": "column_break_21", "fieldtype": "Column Break"}, {"fieldname": "description", "fieldtype": "Text", "label": "Description", "oldfieldname": "description", "oldfieldtype": "Text", "print_width": "300px", "width": "300px"}, {"default": "0", "fieldname": "print_hide", "fieldtype": "Check", "label": "Print Hide", "oldfieldname": "print_hide", "oldfieldtype": "Check"}, {"default": "0", "depends_on": "eval:[\"Int\", \"Float\", \"Currency\", \"Percent\"].indexOf(doc.fieldtype)!==-1", "fieldname": "print_hide_if_no_value", "fieldtype": "Check", "label": "Print Hide If No Value"}, {"description": "Print Width of the field, if the field is a column in a table", "fieldname": "print_width", "fieldtype": "Data", "label": "Print Width", "print_width": "50px", "width": "50px"}, {"depends_on": "eval:parent.istable", "description": "Number of columns for a field in a Grid (Total Columns in a grid should be less than 11)", "fieldname": "columns", "fieldtype": "Int", "label": "Columns"}, {"fieldname": "width", "fieldtype": "Data", "in_list_view": 1, "label": "<PERSON><PERSON><PERSON>", "oldfieldname": "width", "oldfieldtype": "Data", "print_width": "50px", "width": "50px"}, {"default": "0", "fieldname": "is_custom_field", "fieldtype": "Check", "hidden": 1, "label": "Is Custom Field", "read_only": 1}, {"default": "0", "fieldname": "allow_in_quick_entry", "fieldtype": "Check", "label": "Allow in Quick Entry"}, {"fieldname": "property_depends_on_section", "fieldtype": "Section Break", "label": "Property Depends On"}, {"fieldname": "mandatory_depends_on", "fieldtype": "Code", "label": "Mandatory Depends On", "options": "JS"}, {"fieldname": "column_break_33", "fieldtype": "Column Break"}, {"fieldname": "read_only_depends_on", "fieldtype": "Code", "label": "Read Only Depends On", "options": "JS"}, {"default": "0", "depends_on": "eval:!in_list(['Table', 'Table MultiSelect'], doc.fieldtype);", "fieldname": "in_preview", "fieldtype": "Check", "label": "In Preview"}, {"default": "0", "depends_on": "eval:doc.fieldtype=='Duration'", "fieldname": "hide_seconds", "fieldtype": "Check", "label": "Hide Seconds"}, {"default": "0", "depends_on": "eval:doc.fieldtype=='Duration'", "fieldname": "hide_days", "fieldtype": "Check", "label": "Hide Days"}, {"default": "0", "depends_on": "eval:doc.fieldtype=='Section Break'", "fieldname": "hide_border", "fieldtype": "Check", "label": "Hide Border"}, {"default": "0", "depends_on": "eval:in_list([\"Int\", \"Float\", \"Currency\"], doc.fieldtype)", "fieldname": "non_negative", "fieldtype": "Check", "label": "Non Negative"}, {"default": "0", "depends_on": "eval:doc.fieldtype=='Tab Break'", "fieldname": "show_dashboard", "fieldtype": "Check", "label": "Show Dashboard"}, {"default": "0", "fieldname": "no_copy", "fieldtype": "Check", "label": "No Copy"}, {"default": "0", "fieldname": "is_system_generated", "fieldtype": "Check", "hidden": 1, "label": "Is System Generated", "read_only": 1}, {"default": "0", "description": "Don't encode HTML tags like &lt;script&gt; or just characters like &lt; or &gt;, as they could be intentionally used in this field", "fieldname": "ignore_xss_filter", "fieldtype": "Check", "label": "Ignore XSS Filter"}, {"default": "0", "depends_on": "eval: doc.fieldtype === 'Select'", "fieldname": "sort_options", "fieldtype": "Check", "label": "Sort Options"}, {"fieldname": "link_filters", "fieldtype": "JSON", "label": "<PERSON>lters"}, {"fieldname": "placeholder", "fieldtype": "Data", "label": "Placeholder"}], "idx": 1, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-09-28 20:24:03.278884", "modified_by": "Administrator", "module": "Custom", "name": "Customize Form Field", "naming_rule": "Random", "owner": "Administrator", "permissions": [], "sort_field": "creation", "sort_order": "DESC", "states": []}